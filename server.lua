lastRob = {
    [1] = 0,
    [2] = 0,
    [3] = 0,
    [4] = 0,
    [5] = 0,
    [6] = 0,
}
discord = {
    ['webhook'] = 'DISCORDCHANNELWEBHOOKLINK',
    ['name'] = 'rm_fleecaheist',
    ['image'] = 'https://discord.com/api/webhooks/1365802859852992562/5A5Nnzdzz8-9JI1XQa05pnYL2igXi6Sf2A4K9btu-QWXAI5aoYiM_Iq945MonjZ5th4f'
}

ESX = nil

Citizen.CreateThread(function()
    pcall(function() ESX = exports['Framework']:getSharedObject() end)
    if not ESX then
        TriggerEvent('esx:getSharedObject', function(library) 
            ESX = library 
        end)
    end
    ESX.RegisterServerCallback('fleecaheist:server:checkPoliceCount', function(source, cb)
        local src = source
        local players = GetPlayers()
        local policeCount = 0
    
        for i = 1, #players do
            local player = ESX.GetPlayerFromId(players[i])
            if player then
                for k, v in pairs(Config['FleecaMain']['dispatchJobs']) do
                    if player['job']['name'] == v then
                        policeCount = policeCount + 1
                    end
                end
            end
        end
    
        if policeCount >= Config['FleecaMain']['requiredPoliceCount'] then
            cb(true)
        else
            cb(false)
            TriggerClientEvent('fleecaheist:client:showNotification', src, 'Pas assez de policiers dans la ville. (Requis: ' .. Config['FleecaMain']['requiredPoliceCount'] .. ')')
        end
    end)
    
    ESX.RegisterServerCallback('fleecaheist:server:checkTime', function(source, cb, index)
        local src = source
        local player = ESX.GetPlayerFromId(src)
        
        if (os.time() - lastRob[index]) < Config['FleecaHeist'][index]['nextRob'] and lastRob[index] ~= 0 then
            local seconds = Config['FleecaHeist'][index]['nextRob'] - (os.time() - lastRob[index])
            TriggerClientEvent('fleecaheist:client:showNotification', src, Strings['wait_nextheist'] .. ' ' .. math.floor(seconds / 60) .. ' ' .. Strings['minute'])
            cb(false)
        else
            lastRob[index] = os.time()
            discordLog(player.getName() ..  ' - ' .. player.getIdentifier(), ' started the Fleeca Heist!')
            cb(true)
        end
    end)
    
    ESX.RegisterServerCallback('fleecaheist:server:hasItem', function(source, cb, item)
        local src = source
        local player = ESX.GetPlayerFromId(src)
        local playerItem = player.getInventoryItem(item)
    
        if player and playerItem ~= nil then
            if playerItem.count >= 1 then
                cb(true, playerItem.label)
            else
                cb(false, playerItem.label)
            end
        else
            print('[rm_fleecaheist] you need add required items to server database')
        end
    end)
end)

RegisterNetEvent('fleecaheist:server:policeAlert')
AddEventHandler('fleecaheist:server:policeAlert', function(coords)
    local players = ESX.GetPlayers()
    
    for i = 1, #players do
        local player = ESX.GetPlayerFromId(players[i])
        for k, v in pairs(Config['FleecaMain']['dispatchJobs']) do
            if player['job']['name'] == v then
                TriggerClientEvent('fleecaheist:client:policeAlert', players[i], coords)
            end
        end
    end
end)

RegisterServerEvent('fleecaheist:server:rewardItem')
AddEventHandler('fleecaheist:server:rewardItem', function(reward, count)
    local src = source
    local player = ESX.GetPlayerFromId(src)

    print("Reward Item Fleeca Drill Reward: ", json.encode(reward))

    if player then
        if reward.item ~= nil then
            if count ~= nil then
                player.addInventoryItem(reward.item, count)
            else
                player.addInventoryItem(reward.item, reward.count)
            end
        else
            if Config['FleecaMain']["blackMoney"] then
                player.addAccountMoney("black_money", count ~= nil and count or reward.count)
            else
                player.addMoney(count ~= nil and count or reward.count)
            end
        end
    end
end)

RegisterServerEvent('fleecaheist:server:sellRewardItems')
AddEventHandler('fleecaheist:server:sellRewardItems', function()
    local src = source
    local player = ESX.GetPlayerFromId(src)

    if player then
        local totalMoney = 0
        local rewardItems = Config['FleecaMain']['rewardItems']
        local diamondCount = player.getInventoryItem(rewardItems['diamondTrolly']['item']).count
        local goldCount = player.getInventoryItem(rewardItems['goldTrolly']['item']).count

        if diamondCount > 0 then
            player.removeInventoryItem(rewardItems['diamondTrolly']['item'], diamondCount)
            if Config['FleecaMain']["blackMoney"] then
                player.addAccountMoney("black_money", rewardItems['diamondTrolly']['sellPrice'] * diamondCount)
            else
                player.addMoney(rewardItems['diamondTrolly']['sellPrice'] * diamondCount)
            end
            totalMoney = totalMoney + (rewardItems['diamondTrolly']['sellPrice'] * diamondCount)
        end
        if goldCount > 0 then
            player.removeInventoryItem(rewardItems['goldTrolly']['item'], goldCount)
            if Config['FleecaMain']["blackMoney"] then
                player.addAccountMoney("black_money", rewardItems['goldTrolly']['sellPrice'] * goldCount)
            else
                player.addMoney(rewardItems['goldTrolly']['sellPrice'] * goldCount)
            end
            totalMoney = totalMoney + (rewardItems['goldTrolly']['sellPrice'] * goldCount)
        end

        discordLog(player.getName() ..  ' - ' .. player.getIdentifier(), ' Gain $' .. totalMoney .. ' on the Fleeca Heist Buyer!')
        TriggerClientEvent('fleecaheist:client:showNotification', src, Strings['total_money'] .. ' $' .. totalMoney)
        TriggerClientEvent('fleecaheist:client:deletePed', src)
    end
end)

RegisterServerEvent('fleecaheist:server:doorSync')
AddEventHandler('fleecaheist:server:doorSync', function(index)
    TriggerClientEvent('fleecaheist:client:doorSync', -1, index)
end)

RegisterServerEvent('fleecaheist:server:lootSync')
AddEventHandler('fleecaheist:server:lootSync', function(index, type, k)
    TriggerClientEvent('fleecaheist:client:lootSync', -1, index, type, k)
end)

RegisterServerEvent('fleecaheist:server:modelSync')
AddEventHandler('fleecaheist:server:modelSync', function(index, k, model)
    TriggerClientEvent('fleecaheist:client:modelSync', -1, index, k, model)
end)

RegisterServerEvent('fleecaheist:server:grabSync')
AddEventHandler('fleecaheist:server:grabSync', function(index, k, model)
    TriggerClientEvent('fleecaheist:client:grabSync', -1, index, k, model)
end)

RegisterServerEvent('fleecaheist:server:resetHeist')
AddEventHandler('fleecaheist:server:resetHeist', function(index)
    TriggerClientEvent('fleecaheist:client:resetHeist', -1, index)
end)

RegisterCommand('pdfleeca', function(source, args)
    local src = source
    local player = ESX.GetPlayerFromId(src)
    
    if player then
        for k, v in pairs(Config['FleecaMain']['dispatchJobs']) do
            if player['job']['name'] == v then
                return TriggerClientEvent('fleecaheist:client:nearBank', src)
            end
        end
        TriggerClientEvent('fleecaheist:client:showNotification', src, 'You are not cop!')
    end
end)

function discordLog(name, message)
    local data = {
        {
            ["color"] = '3553600',
            ["title"] = "**".. name .."**",
            ["description"] = message,
        }
    }
    PerformHttpRequest(discord['webhook'], function(err, text, headers) end, 'POST', json.encode({username = discord['name'], embeds = data, avatar_url = discord['image']}), { ['Content-Type'] = 'application/json' })
end